"""
爬虫引擎 - 集成反检测和数据提取的核心爬虫引擎
"""
import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass

from crawl4ai import AsyncWebCrawler, CrawlerRunConfig
from crawl4ai.cache_context import CacheMode

from anti_detection import AntiDetectionManager, RequestResult
from extractors import MultiSourceExtractor, StockData, XueqiuHomePageData, StockDetailData, CommentData
from config import AntiDetectionConfig

logger = logging.getLogger(__name__)

@dataclass
class CrawlResult:
    """爬取结果"""
    success: bool
    data: Optional[Union[StockData, XueqiuHomePageData, StockDetailData, List[CommentData]]] = None
    raw_html: Optional[str] = None
    error: Optional[str] = None
    url: Optional[str] = None
    response_time: Optional[float] = None
    attempts: int = 1
    data_type: str = "stock"  # stock, homepage, detail, comments

class XueqiuCrawler:
    """雪球网站专用爬虫"""
    
    def __init__(self, enable_anti_detection: bool = True):
        self.anti_detection = AntiDetectionManager() if enable_anti_detection else None
        self.extractor = MultiSourceExtractor()
        self.session_cookies = {}
        
    async def crawl_stock(self, symbol: str, **kwargs) -> CrawlResult:
        """爬取单个股票数据"""
        url = AntiDetectionConfig.XUEQIU_CONFIG["stock_url_pattern"].format(symbol=symbol)
        return await self.crawl_url(url, **kwargs)
    
    async def crawl_url(self, url: str, **kwargs) -> CrawlResult:
        """爬取指定URL"""
        start_time = time.time()
        
        try:
            if self.anti_detection:
                # 使用反检测管理器
                result = await self.anti_detection.execute_request(
                    self._crawl_with_crawl4ai, url, **kwargs
                )
            else:
                # 直接爬取
                result = await self._crawl_with_crawl4ai(url, **kwargs)
                
            response_time = time.time() - start_time
            
            if result.success and result.data:
                # 提取数据
                stock_data = self.extractor.extract(result.data, url, 'xueqiu')
                return CrawlResult(
                    success=True,
                    data=stock_data,
                    raw_html=result.data,
                    url=url,
                    response_time=response_time
                )
            else:
                return CrawlResult(
                    success=False,
                    error=result.error,
                    url=url,
                    response_time=response_time
                )
                
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"爬取失败 {url}: {e}")
            return CrawlResult(
                success=False,
                error=str(e),
                url=url,
                response_time=response_time
            )
    
    async def _crawl_with_crawl4ai(self, url: str, **kwargs) -> RequestResult:
        """使用crawl4ai进行爬取"""
        try:
            # 准备请求参数
            if self.anti_detection:
                request_params = await self.anti_detection.prepare_request(url)
                browser_config = request_params["browser_config"]
                headers = request_params["headers"]
                proxy = request_params["proxy"]
            else:
                browser_config = {"headless": True}
                headers = {}
                proxy = None
            
            # 创建爬虫配置
            config = CrawlerRunConfig(
                cache_mode=CacheMode.DISABLED,
                **kwargs
            )
            
            # 初始化爬虫
            crawler_kwargs = {
                "browser_type": "chromium",
                "verbose": False,
                **browser_config
            }

            # 如果有代理，添加代理配置
            if proxy:
                crawler_kwargs["proxy"] = proxy.get("http", "").replace("http://", "")

            async with AsyncWebCrawler(**crawler_kwargs) as crawler:
                # 执行爬取，将headers传递给arun方法
                result = await crawler.arun(
                    url=url,
                    config=config,
                    headers=headers,
                    timeout=30000
                )
                
                if result and result.success:
                    # 优先使用HTML，如果没有则使用markdown
                    content = result.html if hasattr(result, 'html') and result.html else result.markdown

                    # 检查JavaScript执行结果
                    js_result = None
                    if hasattr(result, 'js_result') and result.js_result:
                        js_result = result.js_result
                        logger.debug(f"JavaScript执行成功，结果类型: {type(js_result)}")
                    elif hasattr(result, 'js_result'):
                        logger.warning(f"JavaScript执行完成但结果为空: {result.js_result}")

                    request_result = RequestResult(
                        success=True,
                        data=content,
                        status_code=200
                    )

                    # 添加JavaScript结果
                    if js_result is not None:
                        request_result.js_result = js_result

                    return request_result
                else:
                    error_msg = getattr(result, 'error', '爬取失败')
                    logger.warning(f"crawl4ai爬取失败: {error_msg}")
                    if result:
                        logger.debug(f"结果详情: success={getattr(result, 'success', None)}, "
                                   f"status_code={getattr(result, 'status_code', None)}")
                    return RequestResult(
                        success=False,
                        error=error_msg
                    )
                    
        except Exception as e:
            logger.error(f"crawl4ai爬取异常: {e}")
            return RequestResult(
                success=False,
                error=str(e)
            )
    
    async def crawl_multiple_stocks(self, symbols: List[str], 
                                  concurrent_limit: int = 3,
                                  **kwargs) -> List[CrawlResult]:
        """批量爬取多个股票"""
        semaphore = asyncio.Semaphore(concurrent_limit)
        
        async def crawl_with_semaphore(symbol):
            async with semaphore:
                return await self.crawl_stock(symbol, **kwargs)
        
        tasks = [crawl_with_semaphore(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(CrawlResult(
                    success=False,
                    error=str(result),
                    url=f"https://xueqiu.com/S/{symbols[i]}"
                ))
            else:
                processed_results.append(result)
                
        return processed_results
    
    def update_session_cookies(self, cookies: Dict[str, str]):
        """更新会话Cookie"""
        self.session_cookies.update(cookies)

    async def crawl_homepage(self, enhanced_mode=False, **kwargs) -> CrawlResult:
        """爬取雪球首页数据"""
        url = AntiDetectionConfig.XUEQIU_CONFIG["homepage_url"]
        start_time = time.time()

        try:
            if enhanced_mode:
                # 增强模式：支持展开和滚动
                result = await self._crawl_homepage_enhanced(url, **kwargs)
            else:
                # 标准模式
                if self.anti_detection:
                    result = await self.anti_detection.execute_request(
                        self._crawl_with_crawl4ai, url, **kwargs
                    )
                else:
                    result = await self._crawl_with_crawl4ai(url, **kwargs)

            response_time = time.time() - start_time

            if result.success and result.data:
                # 提取首页数据
                homepage_data = self.extractor.extract_homepage(result.data)
                return CrawlResult(
                    success=True,
                    data=homepage_data,
                    raw_html=result.data,
                    url=url,
                    response_time=response_time,
                    data_type="homepage"
                )
            else:
                return CrawlResult(
                    success=False,
                    error=result.error,
                    url=url,
                    response_time=response_time,
                    data_type="homepage"
                )

        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"首页爬取失败: {e}")
            return CrawlResult(
                success=False,
                error=str(e),
                url=url,
                response_time=response_time,
                data_type="homepage"
            )

    async def _crawl_homepage_enhanced(self, url: str, **kwargs):
        """增强版首页爬取，支持展开和滚动"""
        try:
            # 增强版JavaScript代码
            enhancement_js = """
            async function enhanceHomepageContent() {
                console.log('开始增强首页内容获取...');

                // 等待页面完全加载
                await new Promise(resolve => setTimeout(resolve, 2000));

                // 1. 查找并点击所有展开按钮
                let expandSelectors = [
                    'a[class*="expand"]',
                    '.timeline__expand__control',
                    'a:contains("展开")',
                    '[class*="展开"]'
                ];

                let totalExpanded = 0;
                for (let selector of expandSelectors) {
                    try {
                        let buttons = document.querySelectorAll(selector);
                        for (let btn of buttons) {
                            if (btn.offsetParent !== null && !btn.dataset.clicked) {
                                btn.click();
                                btn.dataset.clicked = 'true';
                                totalExpanded++;
                                await new Promise(resolve => setTimeout(resolve, 300));
                            }
                        }
                    } catch (e) {
                        console.log('展开按钮处理失败:', e);
                    }
                }

                // 2. 滚动加载更多内容
                let scrollCount = 0;
                let maxScrolls = 3; // 限制滚动次数避免过长时间
                let lastHeight = document.body.scrollHeight;

                while (scrollCount < maxScrolls) {
                    // 滚动到底部
                    window.scrollTo(0, document.body.scrollHeight);
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // 检查是否有新内容
                    let newHeight = document.body.scrollHeight;
                    if (newHeight === lastHeight) {
                        break;
                    }

                    lastHeight = newHeight;
                    scrollCount++;

                    // 处理新加载的展开按钮
                    for (let selector of expandSelectors) {
                        try {
                            let newButtons = document.querySelectorAll(selector);
                            for (let btn of newButtons) {
                                if (btn.offsetParent !== null && !btn.dataset.clicked) {
                                    btn.click();
                                    btn.dataset.clicked = 'true';
                                    totalExpanded++;
                                    await new Promise(resolve => setTimeout(resolve, 200));
                                }
                            }
                        } catch (e) {
                            console.log('新展开按钮处理失败:', e);
                        }
                    }
                }

                // 3. 最终统计
                let articles = document.querySelectorAll('.style_timeline__item_3WW');

                return {
                    success: true,
                    totalArticles: articles.length,
                    expandedButtons: totalExpanded,
                    scrollCount: scrollCount
                };
            }

            return await enhanceHomepageContent();
            """

            # 使用crawl4ai执行增强爬取
            try:
                from crawl4ai import AsyncWebCrawler
            except ImportError:
                logger.error("crawl4ai未安装，无法使用增强模式")
                return type('Result', (), {
                    'success': False,
                    'data': None,
                    'error': 'crawl4ai未安装，请安装后使用增强模式'
                })()

            async with AsyncWebCrawler(
                headless=True,
                verbose=False,
                **kwargs
            ) as crawler:

                result = await crawler.arun(
                    url=url,
                    js_code=enhancement_js,
                    wait_for="networkidle",
                    delay_before_return_html=3000,
                    css_selector=".style_home__timeline_1Tz"
                )

                if result.success:
                    logger.info("增强版首页爬取成功")
                    return type('Result', (), {
                        'success': True,
                        'data': result.html,
                        'error': None
                    })()
                else:
                    logger.error(f"增强版首页爬取失败: {result.error_message}")
                    return type('Result', (), {
                        'success': False,
                        'data': None,
                        'error': result.error_message
                    })()

        except Exception as e:
            logger.error(f"增强版爬取异常: {e}")
            return type('Result', (), {
                'success': False,
                'data': None,
                'error': str(e)
            })()

    async def crawl_stock_detail(self, symbol: str, **kwargs) -> CrawlResult:
        """爬取个股详细数据"""
        url = AntiDetectionConfig.XUEQIU_CONFIG["stock_url_pattern"].format(symbol=symbol)
        start_time = time.time()

        try:
            if self.anti_detection:
                result = await self.anti_detection.execute_request(
                    self._crawl_with_crawl4ai, url, **kwargs
                )
            else:
                result = await self._crawl_with_crawl4ai(url, **kwargs)

            response_time = time.time() - start_time

            if result.success and result.data:
                # 提取详细数据
                detail_data = self.extractor.extract_stock_detail(result.data, url)
                return CrawlResult(
                    success=True,
                    data=detail_data,
                    raw_html=result.data,
                    url=url,
                    response_time=response_time,
                    data_type="detail"
                )
            else:
                return CrawlResult(
                    success=False,
                    error=result.error,
                    url=url,
                    response_time=response_time,
                    data_type="detail"
                )

        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"个股详细数据爬取失败 {symbol}: {e}")
            return CrawlResult(
                success=False,
                error=str(e),
                url=url,
                response_time=response_time,
                data_type="detail"
            )

    async def crawl_stock_comments(self, symbol: str, count: int = 10, **kwargs) -> CrawlResult:
        """爬取个股评论数据 - 从HTML页面提取，确保数据结构与API一致"""
        # 访问股票详情页面，从HTML中提取评论数据
        url = AntiDetectionConfig.XUEQIU_CONFIG["stock_url_pattern"].format(symbol=symbol)

        start_time = time.time()

        try:
            # 配置爬取参数，针对动态加载内容优化
            crawl_kwargs = {
                "page_timeout": 30000,     # 页面超时30秒
                "delay_before_return_html": 3,  # 返回HTML前等待3秒
                "js_code": [
                    # 等待页面完全加载
                    "await new Promise(resolve => setTimeout(resolve, 3000));",

                    # 尝试点击讨论/评论标签
                    """
                    const tabs = document.querySelectorAll('[data-tab], .tab, .timeline-tab');
                    for (let tab of tabs) {
                        if (tab.textContent.includes('讨论') || tab.textContent.includes('评论') ||
                            tab.getAttribute('data-tab') === 'discuss') {
                            tab.click();
                            await new Promise(resolve => setTimeout(resolve, 2000));
                            break;
                        }
                    }
                    """,

                    # 增强的滚动策略
                    f"""
                    async function enhancedScrollForComments() {{
                        console.log('开始增强滚动获取评论...');

                        let scrollAttempts = {count // 2 + 3};  // 根据需要的评论数调整
                        let currentAttempt = 0;

                        while (currentAttempt < scrollAttempts) {{
                            // 渐进式滚动
                            const scrollStep = (currentAttempt + 1) / scrollAttempts;
                            const targetY = document.body.scrollHeight * scrollStep;

                            window.scrollTo(0, targetY);
                            await new Promise(resolve => setTimeout(resolve, 1500));

                            // 每次滚动后尝试点击加载更多
                            const loadMoreBtns = document.querySelectorAll(
                                '[class*="load"], [class*="more"], button, .load-more, .show-more, [class*="展开"]'
                            );

                            for (let btn of loadMoreBtns) {{
                                const btnText = btn.textContent.toLowerCase();
                                if ((btnText.includes('更多') || btnText.includes('加载') ||
                                     btnText.includes('load') || btnText.includes('more') ||
                                     btnText.includes('展开')) && btn.offsetParent !== null) {{
                                    try {{
                                        btn.click();
                                        console.log(`点击了按钮: ${{btn.textContent}}`);
                                        await new Promise(resolve => setTimeout(resolve, 2000));
                                    }} catch (e) {{
                                        console.log('点击按钮失败:', e);
                                    }}
                                }}
                            }}

                            currentAttempt++;
                        }}

                        // 最终滚动到底部
                        window.scrollTo(0, document.body.scrollHeight);
                        await new Promise(resolve => setTimeout(resolve, 2000));

                        console.log(`滚动完成，共进行 ${{currentAttempt}} 次滚动`);
                        return true;
                    }}

                    await enhancedScrollForComments();
                    """
                ],
                **kwargs
            }

            if self.anti_detection:
                result = await self.anti_detection.execute_request(
                    self._crawl_with_crawl4ai, url, **crawl_kwargs
                )
            else:
                result = await self._crawl_with_crawl4ai(url, **crawl_kwargs)

            response_time = time.time() - start_time

            if result.success and result.data:
                # 从HTML页面提取评论数据，确保数据结构与API一致
                comments = self.extractor.extract_comments(result.data, 'xueqiu')

                # 限制评论数量
                if count and len(comments) > count:
                    comments = comments[:count]

                # 验证并标准化评论数据结构
                validated_comments = self._validate_comment_structure(comments)

                logger.info(f"成功从HTML提取 {len(validated_comments)} 条评论 (请求 {count} 条)")

                return CrawlResult(
                    success=True,
                    data=validated_comments,
                    raw_html=result.data,
                    url=url,
                    response_time=response_time,
                    data_type="comments"
                )
            else:
                return CrawlResult(
                    success=False,
                    error=result.error,
                    url=url,
                    response_time=response_time,
                    data_type="comments"
                )

        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"评论爬取失败 {symbol}: {e}")
            return CrawlResult(
                success=False,
                error=str(e),
                url=url,
                response_time=response_time,
                data_type="comments"
            )

    def _validate_comment_structure(self, comments: List) -> List[Dict[str, Any]]:
        """验证评论数据结构，确保与API格式一致"""
        validated = []

        for comment in comments:
            try:
                # 确保评论对象有必要的属性
                if hasattr(comment, 'content') and comment.content:
                    # 创建标准化的评论数据结构，与API返回格式一致
                    validated_comment = {
                        'id': getattr(comment, 'comment_id', ''),
                        'text': getattr(comment, 'content', ''),
                        'user': {
                            'screen_name': getattr(comment, 'author', ''),
                            'id': getattr(comment, 'author_id', '')
                        },
                        'created_at': getattr(comment, 'publish_time', ''),
                        'fav_count': getattr(comment, 'like_count', 0) or 0,
                        'reply_count': getattr(comment, 'reply_count', 0) or 0
                    }
                    validated.append(validated_comment)

            except Exception as e:
                logger.debug(f"验证评论数据失败: {e}")
                continue

        return validated

class CrawlerMonitor:
    """爬虫监控器"""
    
    def __init__(self):
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_response_time": 0,
            "errors": []
        }
        
    def record_result(self, result: CrawlResult):
        """记录爬取结果"""
        self.stats["total_requests"] += 1
        
        if result.success:
            self.stats["successful_requests"] += 1
        else:
            self.stats["failed_requests"] += 1
            if result.error:
                self.stats["errors"].append({
                    "url": result.url,
                    "error": result.error,
                    "timestamp": time.time()
                })
        
        if result.response_time:
            self.stats["total_response_time"] += result.response_time
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.stats["total_requests"] == 0:
            return 0.0
        return self.stats["successful_requests"] / self.stats["total_requests"]
    
    def get_average_response_time(self) -> float:
        """获取平均响应时间"""
        if self.stats["successful_requests"] == 0:
            return 0.0
        return self.stats["total_response_time"] / self.stats["successful_requests"]
    
    def get_stats_summary(self) -> Dict[str, Any]:
        """获取统计摘要"""
        return {
            "total_requests": self.stats["total_requests"],
            "successful_requests": self.stats["successful_requests"],
            "failed_requests": self.stats["failed_requests"],
            "success_rate": f"{self.get_success_rate():.2%}",
            "average_response_time": f"{self.get_average_response_time():.2f}s",
            "recent_errors": self.stats["errors"][-5:]  # 最近5个错误
        }
    
    def reset_stats(self):
        """重置统计数据"""
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_response_time": 0,
            "errors": []
        }

class SmartCrawler:
    """智能爬虫 - 集成监控和自适应功能"""
    
    def __init__(self, enable_anti_detection: bool = True):
        self.crawler = XueqiuCrawler(enable_anti_detection)
        self.monitor = CrawlerMonitor()
        self.adaptive_delay = 2.0  # 自适应延迟
        
    async def crawl_stock(self, symbol: str, **kwargs) -> CrawlResult:
        """智能爬取股票数据"""
        result = await self.crawler.crawl_stock(symbol, **kwargs)
        self.monitor.record_result(result)

        # 自适应调整
        await self._adaptive_adjustment(result)

        return result

    async def crawl_url(self, url: str, **kwargs) -> CrawlResult:
        """智能爬取指定URL"""
        result = await self.crawler.crawl_url(url, **kwargs)
        self.monitor.record_result(result)

        # 自适应调整
        await self._adaptive_adjustment(result)

        return result
    
    async def crawl_multiple_stocks(self, symbols: List[str], **kwargs) -> List[CrawlResult]:
        """智能批量爬取"""
        results = await self.crawler.crawl_multiple_stocks(symbols, **kwargs)
        
        for result in results:
            self.monitor.record_result(result)
            
        # 批量自适应调整
        await self._batch_adaptive_adjustment(results)
        
        return results
    
    async def _adaptive_adjustment(self, result: CrawlResult):
        """自适应调整策略"""
        if not result.success:
            # 失败时增加延迟
            self.adaptive_delay = min(self.adaptive_delay * 1.5, 30.0)
            await asyncio.sleep(self.adaptive_delay)
        else:
            # 成功时逐渐减少延迟
            self.adaptive_delay = max(self.adaptive_delay * 0.9, 1.0)
    
    async def _batch_adaptive_adjustment(self, results: List[CrawlResult]):
        """批量自适应调整"""
        success_rate = sum(1 for r in results if r.success) / len(results)
        
        if success_rate < 0.5:
            # 成功率低时大幅增加延迟
            self.adaptive_delay = min(self.adaptive_delay * 2.0, 60.0)
            logger.warning(f"成功率低 ({success_rate:.2%})，增加延迟到 {self.adaptive_delay}s")
        elif success_rate > 0.8:
            # 成功率高时减少延迟
            self.adaptive_delay = max(self.adaptive_delay * 0.8, 1.0)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        stats = self.monitor.get_stats_summary()
        stats["adaptive_delay"] = f"{self.adaptive_delay:.2f}s"
        return stats

    async def crawl_homepage(self, **kwargs) -> CrawlResult:
        """智能爬取首页数据"""
        result = await self.crawler.crawl_homepage(**kwargs)
        self.monitor.record_result(result)
        await self._adaptive_adjustment(result)
        return result

    async def crawl_stock_detail(self, symbol: str, **kwargs) -> CrawlResult:
        """智能爬取个股详细数据"""
        result = await self.crawler.crawl_stock_detail(symbol, **kwargs)
        self.monitor.record_result(result)
        await self._adaptive_adjustment(result)
        return result

    async def crawl_stock_comments(self, symbol: str, count: int = 10, **kwargs) -> CrawlResult:
        """智能爬取个股评论"""
        result = await self.crawler.crawl_stock_comments(symbol, count, **kwargs)
        self.monitor.record_result(result)
        await self._adaptive_adjustment(result)
        return result

    async def crawl_comprehensive_stock_data(self, symbol: str, **kwargs) -> Dict[str, CrawlResult]:
        """综合爬取个股所有数据"""
        results = {}

        # 爬取基本股票信息
        logger.info(f"爬取 {symbol} 基本信息...")
        results['basic'] = await self.crawl_stock(symbol, **kwargs)

        # 爬取详细信息
        logger.info(f"爬取 {symbol} 详细信息...")
        results['detail'] = await self.crawl_stock_detail(symbol, **kwargs)

        # 爬取评论
        logger.info(f"爬取 {symbol} 评论...")
        results['comments'] = await self.crawl_stock_comments(symbol, **kwargs)

        return results
