"""
反检测模块 - 核心反爬虫功能实现
"""
import asyncio
import random
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from urllib.parse import urlparse
import json

from config import AntiDetectionConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RequestResult:
    """请求结果数据类"""
    success: bool
    data: Optional[str] = None
    error: Optional[str] = None
    status_code: Optional[int] = None
    response_time: Optional[float] = None
    js_result: Optional[Any] = None  # JavaScript执行结果

class ProxyManager:
    """代理管理器"""
    
    def __init__(self, proxy_list: List[Dict[str, str]] = None):
        self.proxy_list = proxy_list or AntiDetectionConfig.PROXY_POOLS
        self.failed_proxies = set()
        self.current_proxy_index = 0
        
    def get_proxy(self) -> Optional[Dict[str, str]]:
        """获取可用代理"""
        if not self.proxy_list:
            return None
            
        available_proxies = [p for p in self.proxy_list if p not in self.failed_proxies]
        if not available_proxies:
            # 重置失败代理列表
            self.failed_proxies.clear()
            available_proxies = self.proxy_list
            
        return random.choice(available_proxies)
    
    def mark_proxy_failed(self, proxy: Dict[str, str]):
        """标记代理失败"""
        self.failed_proxies.add(str(proxy))
        logger.warning(f"代理失败: {proxy}")
    
    def reset_failed_proxies(self):
        """重置失败代理列表"""
        self.failed_proxies.clear()

class HeaderManager:
    """请求头管理器"""
    
    def __init__(self):
        self.session_headers = {}
        
    def get_headers(self, url: str = None) -> Dict[str, str]:
        """获取请求头"""
        headers = AntiDetectionConfig.get_random_headers()
        
        # 为特定域名添加Referer
        if url:
            parsed_url = urlparse(url)
            if "xueqiu.com" in parsed_url.netloc:
                headers["Referer"] = "https://xueqiu.com/"
                headers["Origin"] = "https://xueqiu.com"
                
        return headers
    
    def update_session_headers(self, new_headers: Dict[str, str]):
        """更新会话头"""
        self.session_headers.update(new_headers)

class FrequencyController:
    """频率控制器"""
    
    def __init__(self):
        self.last_request_time = 0
        self.request_count = 0
        self.error_count = 0
        self.start_time = time.time()
        
    async def wait_if_needed(self):
        """根据频率控制等待"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        # 基础延迟
        min_delay = AntiDetectionConfig.REQUEST_DELAYS["min_delay"]
        
        # 如果请求过于频繁，增加延迟
        if time_since_last < min_delay:
            delay = min_delay - time_since_last
            
            # 如果有错误，增加额外延迟
            if self.error_count > 0:
                delay += AntiDetectionConfig.REQUEST_DELAYS["error_delay"]
                
            logger.info(f"频率控制: 等待 {delay:.2f} 秒")
            await asyncio.sleep(delay)
            
        # 随机延迟，模拟人类行为
        random_delay = AntiDetectionConfig.get_random_delay()
        await asyncio.sleep(random_delay)
        
        self.last_request_time = time.time()
        self.request_count += 1
        
    def record_error(self):
        """记录错误"""
        self.error_count += 1
        
    def record_success(self):
        """记录成功"""
        self.error_count = max(0, self.error_count - 1)

class BrowserConfigManager:
    """浏览器配置管理器"""
    
    @staticmethod
    def get_stealth_config() -> Dict[str, Any]:
        """获取隐身浏览器配置"""
        config = AntiDetectionConfig.BROWSER_CONFIG.copy()
        
        # 添加反检测参数
        config.update({
            "args": [
                "--no-sandbox",
                "--disable-setuid-sandbox", 
                "--disable-dev-shm-usage",
                "--disable-accelerated-2d-canvas",
                "--no-first-run",
                "--no-zygote",
                "--disable-gpu",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--user-agent=" + AntiDetectionConfig.get_random_user_agent()
            ],
            "ignore_default_args": ["--enable-automation"],
            "ignore_https_errors": True
        })
        
        return config
    
    @staticmethod
    def get_mobile_config() -> Dict[str, Any]:
        """获取移动端配置"""
        config = BrowserConfigManager.get_stealth_config()
        config["viewport"] = {"width": 375, "height": 667}
        
        # 移动端User-Agent
        mobile_ua = "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1"
        
        # 更新args中的user-agent
        for i, arg in enumerate(config["args"]):
            if arg.startswith("--user-agent="):
                config["args"][i] = f"--user-agent={mobile_ua}"
                break
                
        return config

class RetryManager:
    """重试管理器"""
    
    def __init__(self):
        self.retry_config = AntiDetectionConfig.RETRY_CONFIG
        
    async def execute_with_retry(self, func, *args, **kwargs) -> RequestResult:
        """带重试的执行函数"""
        last_error = None
        
        for attempt in range(self.retry_config["max_retries"] + 1):
            try:
                result = await func(*args, **kwargs)
                if result.success:
                    return result
                    
                last_error = result.error
                
            except Exception as e:
                last_error = str(e)
                logger.error(f"执行失败 (尝试 {attempt + 1}): {e}")
                
            if attempt < self.retry_config["max_retries"]:
                delay = self.retry_config["retry_delay"] * (
                    self.retry_config["backoff_factor"] ** attempt
                )
                logger.info(f"等待 {delay} 秒后重试...")
                await asyncio.sleep(delay)
                
        return RequestResult(success=False, error=last_error)

class AntiDetectionManager:
    """反检测管理器 - 统一管理所有反检测功能"""
    
    def __init__(self):
        self.proxy_manager = ProxyManager()
        self.header_manager = HeaderManager()
        self.frequency_controller = FrequencyController()
        self.retry_manager = RetryManager()
        
    async def prepare_request(self, url: str) -> Dict[str, Any]:
        """准备请求参数"""
        # 频率控制
        await self.frequency_controller.wait_if_needed()
        
        # 获取代理和请求头
        proxy = self.proxy_manager.get_proxy()
        headers = self.header_manager.get_headers(url)
        
        # 获取浏览器配置
        browser_config = BrowserConfigManager.get_stealth_config()
        
        return {
            "proxy": proxy,
            "headers": headers,
            "browser_config": browser_config
        }
    
    def handle_request_result(self, result: RequestResult, proxy: Dict[str, str] = None):
        """处理请求结果"""
        if result.success:
            self.frequency_controller.record_success()
        else:
            self.frequency_controller.record_error()
            if proxy:
                self.proxy_manager.mark_proxy_failed(proxy)
                
    async def execute_request(self, request_func, *args, **kwargs) -> RequestResult:
        """执行带反检测的请求"""
        return await self.retry_manager.execute_with_retry(request_func, *args, **kwargs)
